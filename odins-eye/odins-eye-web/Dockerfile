FROM ubuntu:22.04

# Set working directory
WORKDIR /root/

# Install system dependencies
RUN apt-get update --allow-releaseinfo-change && \
    apt-get install -y --no-install-recommends \
    supervisor build-essential cmake gcc g++ git curl wget nginx \
    libpq-dev libssl-dev libffi-dev python3.11 python3.11-venv python3.11-dev python3-pip \
    libopenblas-dev liblapack-dev libatlas-base-dev gfortran libblis3 && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Set up Python virtual environment
RUN python3.11 -m venv /root/venv
ENV PATH="/root/venv/bin:$PATH"

# Install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip setuptools wheel cython && \
    pip install --no-cache-dir --prefer-binary --no-build-isolation numpy && \
    pip install --no-cache-dir --prefer-binary --no-build-isolation -r requirements.txt && \
    pip install --no-cache-dir --prefer-binary --no-build-isolation blis thinc

# Download spaCy model
RUN python -m spacy download en_core_web_trf

# Copy application code
COPY backend /root/backend
COPY frontend /usr/share/nginx/html
COPY supervisord.conf /etc/supervisor/supervisord.conf
COPY nginx.conf /etc/nginx/nginx.conf

# Set permissions
RUN chmod -R 755 /usr/share/nginx/html && chmod 644 /etc/nginx/nginx.conf

# Set environment variables
ENV TRANSFORMERS_CACHE=/root/models \
    HF_HOME=/root/models

# Expose Nginx port
EXPOSE 80

# Start services
CMD ["supervisord", "-c", "/etc/supervisor/supervisord.conf", "-n"]