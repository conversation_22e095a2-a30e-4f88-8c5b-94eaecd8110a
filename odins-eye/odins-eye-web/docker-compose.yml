services:
  database:
    image: "redis:alpine"
    container_name: odins-eye-database
    ports:
      - "6379:6379"
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      retries: 5
      start_period: 5s
      timeout: 3s

  odins-eye:
    build: .
    container_name: odins-eye-app
    env_file:
      - .env
    ports:
      - 80:80
    depends_on:
      database:
        condition: service_healthy
    volumes:
      - ./models:/app/models
      - ./data:/app/data
    environment:
      - TRANSFORMERS_CACHE=/app/models
      - HF_HOME=/app/models