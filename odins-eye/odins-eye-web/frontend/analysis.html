<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>'s Eye Analysis</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css"
        integrity="sha512-KQ4LFDduR4dtMaPTutLmkdFquFICPNMWsRkkcx0E+BeSgDZ/OWp/IaXhPg6xrl93OxCL6x/UkChvKzTXY+vrmw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="icon" type="image/png" href="/assets/logo.png">
    <style>
        /* 🎨 Modern Color Palette & CSS Variables */
        :root {
            --primary: #6366F1;
            --primary-light: rgba(99, 102, 241, 0.1);
            --primary-medium: rgba(99, 102, 241, 0.2);
            --shadow-sm: 0 10px 20px rgba(0, 0, 0, 0.15);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Ripple effect container */
        .ripple-button {
            position: relative;
            overflow: hidden;
            /* Ensures ripple is clipped within button bounds */
        }

        /* The ripple span */
        .ripple {
            position: absolute;
            border-radius: 9999px;
            /* Fully round */
            transform: scale(0);
            animation: ripple 600ms linear;
            background-color: rgba(255, 255, 255, 0.4);
            pointer-events: none;
            /* Don’t block clicks */
        }

        /* Keyframes for the expanding circle */
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Enhanced Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: var(--primary-light);
            text-align: left;
            padding: 10px;
            font-weight: bold;
        }

        td {
            padding: 10px;
            border-bottom: 1px solid #e5e7eb;
        }

        tr:nth-child(even) {
            background: #f9fafb;
            /* Alternating row colors */
        }

        tr:hover {
            background: rgba(99, 102, 241, 0.1);
            transition: background 0.3s ease-in-out;
        }

        @media (prefers-color-scheme: dark) {
            body {
                background: #181818;
                color: #f5f5f5;
            }

            .container {
                background: #222;
            }

            table {
                background: #333;
            }

            th,
            td {
                color: white;
            }
        }

        /* Responsive Table Scroll */
        @media (max-width: 768px) {
            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .grid {
                display: flex;
                flex-direction: column;
            }

            .col-span-6 {
                width: 100%;
                border: none;
            }
        }

        /* Floating Action Button for 'New Scan' */
        .fab {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--primary);
            color: #fff;
            padding: 12px 16px;
            border-radius: 50px;
            box-shadow: var(--shadow-md);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: 0.3s;
            z-index: 50;
        }

        .fab:hover {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            transform: translateY(-4px);
        }

        /* 🎯 Enhanced Active Tab with Animation */
        .tab-active {
            border-bottom: 3px solid var(--primary);
            color: var(--primary);
            font-weight: 600;
            position: relative;
            transition: var(--transition);
        }

        .tab-active::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary);
            transform-origin: right;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: scaleX(0);
            }

            to {
                transform: scaleX(1);
            }
        }

        .btn-primary {
            background-color: var(--primary);
            color: #fff;
            padding: 10px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .btn-primary:hover {
            background-color: #4f51c6;
            /* a darker shade of #6366F1 */
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }

        /* 🌟 Sophisticated Loader */
        .loader {
            border: 2px solid transparent;
            border-top: 2px solid var(--primary);
            border-right: 2px solid var(--primary);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
            filter: drop-shadow(0 0 2px rgba(99, 102, 241, 0.3));
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 🎪 Elevated Container Design */
        .container {
            background: rgba(38, 174, 208, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md),
                0 0 0 1px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            padding: 0;
            margin: 0;
            max-width: 100%;
            width: 100%;
            transition: var(--transition);
            opacity: 0;
            transform: translateY(20px);
            /* We'll fade/slide it in for that React-like feel */
        }

        /* On load, fade the container in */
        .container.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .container:hover {
            transform: translateY(-2px) scale(1.01);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
        }

        /* 🎭 Advanced Tab Interface */
        .tab-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0;
            margin: 0;
            gap: 0;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            position: sticky;
        }

        .tab-container button {
            border-radius: 8px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .tab-container button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            background: var(--primary-light);
            transform: translate(-50%, -50%) scale(0);
            border-radius: inherit;
            transition: var(--transition);
        }

        .tab-container button:hover::before {
            transform: translate(-50%, -50%) scale(1);
        }

        /* 🎪 Email View Button Enhancements */
        .email-view-btn {
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            background: var(--primary-light);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .email-view-btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: linear-gradient(45deg,
                    transparent 0%,
                    rgba(255, 255, 255, 0.2) 50%,
                    transparent 100%);
            transform: translateX(-100%);
        }

        .email-view-btn:hover::after {
            transform: translateX(100%);
            transition: transform 0.6s;
        }

        .email-view-active {
            background: var(--primary);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .email-content {
            line-height: 1.6;
            padding: 5px;
            margin: 0;
        }

        .email-content strong {
            color: var(--primary);
        }

        .email-content p {
            margin-bottom: 0px;
        }

        /* 🎨 Refined Background */
        body {
            background: linear-gradient(135deg, #3d658e, #3d658e);
            min-height: 100vh;
        }

        /* 🎯 Polished Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 8px;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 8px;
            border: 2px solid #f1f1f1;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Hidden state */
        .raw-data {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out, opacity 0.2s ease-in-out;
            opacity: 0;
        }

        /* Shown state */
        .raw-data.active {
            max-height: 500px;
            /* Adjust as needed */
            opacity: 1;
        }

        /* Simple fade/slide-in animation */
        @keyframes fadeInSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInSlideDown {
            animation: fadeInSlide 0.6s ease-out both;
        }
    </style>
</head>

<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"
    integrity="sha512-/OKBnrEUwPT6Xqp6FOd79yZmlHua1rwR7mGr8so7o0Fm7SEK5Cb/mxhnIecsvf74QZeG8yeM/BCe5zA9VzqCsw=="
    crossorigin="anonymous" referrerpolicy="no-referrer">
    </script>

<body class="bg-gradient-to-br from-gray-50 to-gray-100">
    <!-- Branded Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
            <!-- Left: Logo + Brand -->
            <div class="flex items-center gap-3">
                <!-- Replace with your own logo path -->
                <img src="/assets/logo.png" alt="Odin's Eye Logo" class="h-8 w-8">
                <span class="text-xl font-bold text-gray-800">Odin's Eye</span>
            </div>

            <!-- Right: Tagline or nav links -->
            <div class="hidden sm:block text-sm text-gray-600 font-medium">
                Email Analysis
            </div>
        </div>
    </header>

    <!-- "No Data" Container (Hidden by default) -->
    <div id="noDataContainer" class="hidden min-h-screen flex flex-col items-center justify-center p-6 text-center">
        <!-- Example illustration from undraw.co -->
        <img src="/assets/undraw_no_data.svg" alt="No data" class="w-64 h-auto mb-4">
        <h2 class="text-2xl font-semibold mb-2 text-gray-800">No Data Found</h2>
        <p class="text-gray-600 mb-4">
            We couldn't find any analysis results. Please run a new scan or try again later.
        </p>
        <a href="index.html" class="btn-primary inline-block mt-2">
            Go to New Scan
        </a>
    </div>

    <!-- Floating Action Button for "New Scan" -->
    <a href="index.html" class="fab ripple-button" title="New Scan">
        <!-- An inline SVG for the arrow icon -->
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        <span>New Scan</span>
    </a>

    <!-- Main Container -->
    <div class="container mx-auto max-w-[95%] max-h-[95vh] overflow-auto bg-white/95 backdrop-blur-sm shadow-xl rounded-xl my-6 border border-gray-200/50 animate-in"
        id="mainContainer">
        <!-- We'll fade this in with JavaScript after DOM load -->

        <!-- Header Section -->
        <div class="border-b px-8 py-5 flex justify-between items-center bg-white rounded-t-xl">
            <h1 class="text-xl font-semibold flex items-center gap-3">
                Analysis:
                <span id="emailSubject" class="text-blue-600 font-medium relative group">
                    Loading...
                    <span
                        class="absolute h-0.5 bottom-0 left-0 bg-blue-600/30 w-full transform origin-left transition-transform scale-x-0 group-hover:scale-x-100"></span>
                </span>
            </h1>
            <div class="text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-full shadow-sm" id="analysisTimestamp"></div>
        </div>

        <!-- Navigation Tabs -->
        <div class="sticky top-0 z-20 bg-white/80 backdrop-blur-sm shadow-md">
            <div class="flex space-x-2 px-6 py-3">
                <!-- Example: Tab with icon -->
                <button
                    class="tab-active px-4 py-2 hover:bg-blue-50 rounded-lg transition-all duration-200 font-medium flex items-center gap-2 ripple-button"
                    data-tab="headers">
                    <!-- inline mail svg icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8m-2 8V8a2 2 0 00-2-2H7a2 2 0 00-2 2v8" />
                    </svg>
                    <span>Headers</span>
                </button>

                <button
                    class="px-4 py-2 hover:bg-blue-50 rounded-lg transition-all duration-200 text-gray-600 hover:text-gray-900 flex items-center gap-2 ripple-button"
                    data-tab="traceroute">
                    <!-- activity / traceroute icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M22 12h-6l-2 8L9 4l-2 8H2" />
                    </svg>
                    <span>Traceroute</span>
                </button>

                <button
                    class="px-4 py-2 hover:bg-blue-50 rounded-lg transition-all duration-200 text-gray-600 hover:text-gray-900 flex items-center gap-2 ripple-button"
                    data-tab="xheaders">
                    <!-- Alert Circle icon, for example -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M12 8v4m0 4h.01m6.938-4A7.938 7.938 0 1112 4a7.938 7.938 0 016.938 8z" />
                    </svg>
                    <span>X-Headers</span>
                </button>

                <button
                    class="px-4 py-2 hover:bg-blue-50 rounded-lg transition-all duration-200 text-gray-600 hover:text-gray-900 flex items-center gap-2 ripple-button"
                    data-tab="security">
                    <!-- shield icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M12 22c5.52-1.33 8-6.67 8-10V5l-8-3-8 3v7c0 3.33 2.48 8.67 8 10z" />
                    </svg>
                    <span>Security</span>
                </button>

                <button
                    class="px-4 py-2 hover:bg-blue-50 rounded-lg transition-all duration-200 text-gray-600 hover:text-gray-900 flex items-center gap-2 ripple-button"
                    data-tab="attachments">
                    <!-- Paperclip icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M21.44 11.05l-9-9a4 4 0 00-5.65 5.66l9 9a2 2 0 102.83-2.83l-8.59-8.59" />
                    </svg>
                    <span>Attachments</span>
                </button>

                <button
                    class="px-4 py-2 hover:bg-blue-50 rounded-lg transition-all duration-200 text-gray-600 hover:text-gray-900 flex items-center gap-2 ripple-button"
                    data-tab="iocs">
                    <!-- search icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M11 19a8 8 0 100-16 8 8 0 000 16z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-4.35-4.35" />
                    </svg>
                    <span>IOCs</span>
                </button>
            </div>
        </div>

        <!-- Main Content Layout -->
        <div class="grid grid-cols-12 min-h-screen">
            <!-- Left Panel (Tab Content) -->
            <div class="col-span-6 border-r p-6 bg-white overflow-auto">
                <div id="tabContent" class="space-y-4 divide-y divide-gray-100">
                    <!-- Tab content will be inserted here -->
                </div>
            </div>

            <!-- Email Render Area -->
            <div class="col-span-6 p-6 bg-gray-50/50">
                <div class="bg-white border rounded-xl p-5 shadow-sm h-full hover:shadow-md transition-shadow">
                    <h2 class="text-xl font-bold mb-4 text-gray-800">Email Content</h2>
                    <div class="flex border-b mb-6 bg-gray-50 rounded-t-lg overflow-hidden">
                        <button class="tab-active px-5 py-3 hover:bg-blue-50 transition-all duration-200"
                            data-view="rendered">Rendered</button>
                        <button class="px-5 py-3 hover:bg-blue-50 transition-all duration-200 text-gray-600"
                            data-view="plaintext">Plaintext</button>
                        <button class="px-5 py-3 hover:bg-blue-50 transition-all duration-200 text-gray-600"
                            data-view="html">HTML</button>
                        <button class="px-5 py-3 hover:bg-blue-50 transition-all duration-200 text-gray-600"
                            data-view="source">Source</button>
                    </div>

                    <div id="emailContent" class="relative rounded-lg overflow-hidden">
                        <!-- Rendered (iframe) -->
                        <iframe id="emailRender"
                            class="w-full h-[500px] border border-gray-100 bg-white rounded-lg shadow-inner"></iframe>

                        <!-- Plaintext block (no language) -->
                        <pre id="emailPlaintext"
                            class="language-none hidden bg-gray-50 p-4 rounded-lg font-mono text-sm"></pre>

                        <!-- HTML block -->
                        <pre id="emailHtml"
                            class="language-html hidden bg-gray-50 p-4 rounded-lg font-mono text-sm"></pre>

                        <!-- JSON block (for raw source or full JSON) -->
                        <pre id="emailSource"
                            class="language-json hidden bg-gray-50 p-4 rounded-lg font-mono text-sm"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            border: 2px solid #f1f1f1;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Active tab styling */
        .tab-active {
            @apply text-blue-600 bg-blue-50 font-medium relative;
        }

        .tab-active::after {
            content: '';
            @apply absolute bottom-0 left-0 w-full h-0.5 bg-blue-600;
            animation: slideIn 0.3s ease-out forwards;
        }

        @keyframes fadeInSlideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInSlideDown {
            animation: fadeInSlideDown 0.6s ease-out both;
        }

        .animate-ping {
            animation: ping 1s infinite;
        }

        .scale-y-0 {
            transform: scaleY(0);
            height: 0;
            opacity: 0;
        }

        .scale-y-100 {
            transform: scaleY(1);
            height: auto;
            opacity: 1;
            transition: all 0.3s ease-in-out;
        }
    </style>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const emailData = JSON.parse(localStorage.getItem("emailData"));
            const noDataContainer = document.getElementById("noDataContainer");
            const mainContainer = document.getElementById("mainContainer");

            // If no data, show the "No Data" container and hide the main container
            if (!emailData) {
                noDataContainer.classList.remove("hidden");
                mainContainer.classList.add("hidden");
                return;
            }

            // Otherwise, show the main container and hide the "No Data" container
            noDataContainer.classList.add("hidden");
            mainContainer.classList.remove("hidden");

            // Fade in the container (once it's visible)
            requestAnimationFrame(() => {
                mainContainer.classList.add("animate-in");
            });

            document.getElementById("emailSubject").textContent = emailData.analysis?.headers?.subject || "Unknown Subject";
            const timestamp = new Date().toLocaleString("en-US", {
                weekday: "short",
                month: "short",
                day: "numeric",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit"
            });
            document.getElementById("analysisTimestamp").textContent = `Analyzed on: ${timestamp}`;

            function renderTable(headers, data) {
                return `
                    <table class="w-full border-collapse border border-gray-300 text-sm">
                        <thead>
                            <tr class="bg-gray-100">
                                ${headers.map(header => `<th class="border border-gray-300 px-2 py-1">${header}</th>`).join("")}
                            </tr>
                        </thead>
                        <tbody>
                            ${data.length > 0 ? data.map(row => `<tr>${row.map(cell => `<td class="border px-2 py-1">${cell}</td>`).join("")}</tr>`).join("") : `<tr><td colspan="${headers.length}" class="text-center border border-gray-300 p-2">No data available</td></tr>`}
                        </tbody>
                    </table>`;
            }

            function renderHeaders() {
                const headers = emailData.analysis?.headers || {};
                const ordered = [];

                // Insert headers in desired order
                if (headers["in-reply-to"]) ordered.push(["In-Reply-To", headers["in-reply-to"]]);
                if (headers["date"]) ordered.push(["Date", headers["date"]]);
                if (headers["reply-to"]) ordered.push(["Reply-To", formatEmail(headers["reply-to"])]);

                // Add the rest, excluding already added fields
                Object.entries(headers).forEach(([key, value]) => {
                    const keyLower = key.toLowerCase();
                    if (["in-reply-to", "reply-to", "date"].includes(keyLower)) return;

                    if (["from", "to", "cc", "bcc"].includes(keyLower)) {
                        ordered.push([key, formatEmail(value)]);
                    } else {
                        ordered.push([key, value]);
                    }
                });

                document.getElementById("tabContent").innerHTML = renderTable(
                    ["Field", "Value"],
                    ordered
                );
            }

            function formatEmail(rawData) {
                if (!rawData) return "N/A";

                // Handle both single and multiple email addresses
                return (Array.isArray(rawData) ? rawData : [rawData]).map(entry => {
                    const match = entry.match(/(.*)<(.*)>/); // Matches "Name <email>"
                    if (match) {
                        return `<span class="font-semibold">${match[1].trim()}</span> <br> 
                                <span class="text-gray-600">${match[2].trim()}</span>`;
                    }
                    return `<span class="text-gray-600">${entry.trim()}</span>`; // If no name, show just email
                }).join("<br>");
            }

            function renderTraceroute() {
                const traceroute = emailData.analysis?.traceroute || [];

                if (traceroute.length === 0) {
                    document.getElementById("tabContent").innerHTML = `<p class="text-center text-gray-500">No traceroute data available.</p>`;
                    return;
                }

                let content = `<div class="relative">`;

                traceroute.forEach((hop, index) => {
                    const delay = index * 100; // Increasing delay for each hop

                    content += `
                    <div class="flex items-start space-x-4 animate-fadeInSlideDown" style="animation-delay: ${delay}ms;">
                        <div class="relative w-6 flex flex-col items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-ping"></div>
                            ${index < traceroute.length - 1 ? `<div class="w-1 h-12 bg-blue-300"></div>` : ""}
                        </div>
                        <div class="bg-white shadow-md border p-4 rounded-lg w-full">
                            <h3 class="text-lg font-semibold text-gray-700">Hop ${hop.hop}</h3>
                            <p class="text-sm text-gray-500">${hop.timestamp}</p>
                            ${hop.received_from ? `<p><strong>Received from:</strong> <span class="text-blue-600">${hop.received_from}</span> ${hop.originating_ip !== "Unknown" ? `(<span class="text-red-500">${hop.originating_ip}</span>)` : ""}</p>` : ""}
                            ${hop.received_by ? `<p><strong>Received by:</strong> <span class="text-blue-600">${hop.received_by}</span></p>` : ""}
                            
                            <div id="rawHop${index}" class="hidden transition-all duration-300 ease-in-out transform scale-y-0 origin-top">
                                <pre class="mt-2 p-3 bg-gray-100 rounded-lg font-mono text-sm border">${hop.raw}</pre>
                            </div>
                        </div>
                    </div>`;
                });

                content += `</div>`;

                document.getElementById("tabContent").innerHTML = content;
            }

            function renderAttachments() {
                document.getElementById("tabContent").innerHTML = renderTable(
                    ["Filename", "SHA-256"],
                    emailData.analysis?.attachments.map(att => [
                        att.filename || "Unknown",
                        att.sha256 || "N/A"
                    ]) || []
                );
            }

            function renderIOCs() {
                // Pull IOCs from your `emailData`
                const iocs = emailData.analysis?.iocs || {};
                const iocTypes = ["urls", "domains", "emails", "ip_addresses", "phone_numbers", "social_media", "hashes"];

                // Build a raw array of [TYPE, VALUE]
                const rawIOCData = [];
                iocTypes.forEach(type => {
                    if (Array.isArray(iocs[type]) && iocs[type].length > 0) {
                        iocs[type].forEach(value => {
                            // e.g. ["URLS", "http://example.com"]
                            rawIOCData.push([type.replace("_", " ").toUpperCase(), value]);
                        });
                    }
                });

                // STEP A: Create the UI for search & filter buttons, plus a container for the table
                document.getElementById("tabContent").innerHTML = `
                  <div class="flex flex-wrap items-center space-x-2 mb-4">
                    <!-- Search box -->
                    <input
                      type="text"
                      id="iocSearchInput"
                      class="border border-gray-300 rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-300"
                      placeholder="Search IOCs..."
                    />
              
                    <!-- Filter Chips (buttons) -->
                    <button data-filter="ALL"
                      class="ioc-filter-btn px-3 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300">
                      All
                    </button>
                    <button data-filter="URLS"
                      class="ioc-filter-btn px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                      URLs
                    </button>
                    <button data-filter="DOMAINS"
                      class="ioc-filter-btn px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                      Domains
                    </button>
                    <button data-filter="EMAILS"
                      class="ioc-filter-btn px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                      Emails
                    </button>
                    <button data-filter="IP ADDRESSES"
                      class="ioc-filter-btn px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                      IPs
                    </button>
                    <!-- Add more buttons for phone_numbers, social_media, etc. if desired -->
                  </div>
              
                  <!-- The table will render here -->
                  <div id="iocTableContainer"></div>
                `;

                // STEP B: A helper function to actually place the table HTML in the DOM
                function doRenderTable(filteredData) {
                    document.getElementById("iocTableContainer").innerHTML = renderTable(
                        ["Type", "Value"],
                        filteredData.length > 0 ? filteredData : [["No IOCs Found", ""]]
                    );
                }

                // STEP C: The main filtering logic (by type and search term)
                function filterData(filterType, searchTerm) {
                    let filtered = [...rawIOCData];

                    // 1) Filter by Type
                    if (filterType !== "ALL") {
                        // Note that our row[0] is already uppercase, e.g. "URLS", "IP ADDRESSES", etc.
                        filtered = filtered.filter(row => row[0] === filterType);
                    }

                    // 2) Filter by Search
                    if (searchTerm && searchTerm.trim().length > 0) {
                        const lower = searchTerm.toLowerCase();
                        filtered = filtered.filter(row =>
                            row[0].toLowerCase().includes(lower) ||
                            row[1].toLowerCase().includes(lower)
                        );
                    }

                    return filtered;
                }

                // STEP D: Do an initial render (show everything)
                doRenderTable(rawIOCData);

                // STEP E: Wire up the search box
                const searchInput = document.getElementById("iocSearchInput");
                searchInput.addEventListener("input", (e) => {
                    const searchValue = e.target.value;
                    // Find which filter button is active
                    const activeBtn = document.querySelector(".ioc-filter-btn.active");
                    const filterType = activeBtn ? activeBtn.getAttribute("data-filter") : "ALL";
                    // Re-render
                    doRenderTable(filterData(filterType, searchValue));
                });

                // STEP F: Wire up the filter buttons
                const filterButtons = document.querySelectorAll(".ioc-filter-btn");
                filterButtons.forEach(btn => {
                    btn.addEventListener("click", () => {
                        // Remove .active from all buttons
                        filterButtons.forEach(b => b.classList.remove("active", "bg-blue-500", "text-white"));
                        // Mark this button as active
                        btn.classList.add("active", "bg-blue-500", "text-white");

                        const filterType = btn.getAttribute("data-filter");
                        const searchValue = searchInput.value;
                        // Re-render
                        doRenderTable(filterData(filterType, searchValue));
                    });
                });
            }

            // ✅ Helper: Extract domain from address
            function extractDomain(address) {
                if (!address || address === "N/A") return null;
                const atIndex = address.indexOf("@");
                if (atIndex !== -1) {
                    return address.slice(atIndex + 1).replace(/[>\s]/g, '').trim();
                }
                return address.trim();
            }

            // ✅ Helper: Normalize status
            function normalizeStatus(rawStatus) {
                if (!rawStatus) return "UNKNOWN";
                switch (rawStatus.toLowerCase()) {
                    case "pass": return "PASS";
                    case "fail": return "FAIL";
                    case "softfail": return "SOFTFAIL";
                    case "neutral": return "NEUTRAL";
                    case "none": return "NONE";
                    case "temperror": return "TEMPERROR";
                    case "permerror": return "PERMERROR";
                    default: return rawStatus.toUpperCase();
                }
            }

            // ✅ Fetch SPF record
            async function fetchSPFRecord(domain) {
                try {
                    const response = await fetch(`https://dns.google/resolve?name=${domain}&type=TXT`);
                    const data = await response.json();
                    if (!data.Answer) return "SPF record not found.";
                    const spfRecord = data.Answer.find(record => record.data.includes("v=spf1"));
                    return spfRecord ? spfRecord.data.replace(/"/g, '') : "No SPF record found.";
                } catch (error) {
                    console.error("Error fetching SPF record:", error);
                    return "Error fetching SPF.";
                }
            }

            // ✅ Fetch DMARC record
            async function fetchDMARCRecord(domain) {
                try {
                    const response = await fetch(`https://dns.google/resolve?name=_dmarc.${domain}&type=TXT`);
                    const data = await response.json();
                    if (!data.Answer) return "DMARC record not found.";
                    const dmarcRecord = data.Answer.find(record => record.data.includes("v=DMARC1"));
                    return dmarcRecord ? dmarcRecord.data.replace(/"/g, '') : "No DMARC record found.";
                } catch (error) {
                    console.error("Error fetching DMARC record:", error);
                    return "Error fetching DMARC.";
                }
            }

            // ✅ Fetch DKIM record
            async function fetchDKIMRecord(selector, domain) {
                try {
                    const response = await fetch(`https://dns.google/resolve?name=${selector}._domainkey.${domain}&type=TXT`);
                    const data = await response.json();
                    if (!data.Answer) return "DKIM record not found.";
                    return data.Answer.map(record => record.data.replace(/"/g, '')).join("<br>");
                } catch (error) {
                    console.error("Error fetching DKIM record:", error);
                    return "Error fetching DKIM.";
                }
            }

            // ✅ Helper: Check if From domain can be spoofed
            function canDomainBeSpoofed(dmarcRecord, spfStatus, dkimStatus, dmarcStatus) {
                if (!dmarcRecord || dmarcRecord === "DMARC record not found.") {
                    return true;
                }

                const hasRejectPolicy = dmarcRecord.includes("p=reject");
                const hasQuarantinePolicy = dmarcRecord.includes("p=quarantine");

                if (!hasRejectPolicy && !hasQuarantinePolicy) {
                    return true; // Only monitoring
                }

                // If DMARC failed, can be spoofed
                if (dmarcStatus === "FAIL") {
                    return true;
                }

                // If both SPF and DKIM fail
                if (spfStatus === "FAIL" && dkimStatus === "FAIL") {
                    return true;
                }

                return false; // Protected
            }

            // ✅ Cache
            let cachedSecurityData = null;

            // ✅ Main function
            async function fetchSecurityData() {
                if (cachedSecurityData) return;

                const headers = emailData.analysis?.headers || {};
                const authResults = headers["authentication-results"] || "";

                // Parse
                const originatingIpMatch = authResults.match(/sender IP is ([\d.:a-f]+)/i);
                const originatingIp = originatingIpMatch ? originatingIpMatch[1] : "N/A";

                const returnPathMatch = authResults.match(/smtp\.mailfrom=([^;]+)/i);
                const returnPathDomain = returnPathMatch ? returnPathMatch[1].trim() : "N/A";

                const fromDomainMatch = authResults.match(/header\.from=([^;]+)/i);
                const fromDomain = fromDomainMatch ? fromDomainMatch[1].trim() : "N/A";

                const dkimSelectorMatch = authResults.match(/header\.s=([^;]+)/i);
                const dkimSelector = dkimSelectorMatch ? dkimSelectorMatch[1].trim() : "N/A";

                const dkimDomainMatch = authResults.match(/header\.d=([^;]+)/i);
                const dkimDomain = dkimDomainMatch ? dkimDomainMatch[1].trim() : (returnPathDomain || fromDomain || "N/A");

                const spfMatch = authResults.match(/spf=([a-z]+)/i);
                const spfStatus = normalizeStatus(spfMatch ? spfMatch[1] : null);

                const dkimMatch = authResults.match(/dkim=([a-z]+)/i);
                const dkimStatus = normalizeStatus(dkimMatch ? dkimMatch[1] : null);

                const dmarcMatch = authResults.match(/dmarc=([a-z]+)/i);
                const dmarcStatusRaw = dmarcMatch ? dmarcMatch[1] : null;
                let dmarcStatus = normalizeStatus(dmarcStatusRaw);

                const dkimSignatureCountMatch = authResults.match(/dkim=([a-z]+)/gi);
                const dkimSignatureCount = dkimSignatureCountMatch ? dkimSignatureCountMatch.length : 0;

                const domainForLookup = extractDomain(returnPathDomain) || extractDomain(fromDomain) || dkimDomain;

                const spfRecord = domainForLookup ? await fetchSPFRecord(domainForLookup) : "No domain to check SPF.";
                const dmarcRecord = domainForLookup ? await fetchDMARCRecord(domainForLookup) : "No domain to check DMARC.";
                const dkimRecord = (dkimSelector !== "N/A" && dkimDomain !== "N/A")
                    ? await fetchDKIMRecord(dkimSelector, dkimDomain)
                    : "No DKIM selector or domain found.";

                // ✅ Remove forced DMARC override logic — trust dmarc=pass or fail directly

                // ✅ Spoofability verdict
                const spoofable = canDomainBeSpoofed(dmarcRecord, spfStatus, dkimStatus, dmarcStatus);

                cachedSecurityData = {
                    spfStatus,
                    dkimStatus,
                    dmarcStatus,
                    dkimSelector,
                    signingDomain: dkimDomain,
                    originatingIp,
                    returnPathDomain,
                    dkimSignatureCount,
                    spfRecord,
                    dmarcRecord,
                    dkimRecord,
                    spoofable
                };
            }

            // ✅ Fetch security data ONCE when email is analyzed
            async function analyzeEmail() {
                await fetchSecurityData(); // Runs once when email is submitted
                renderHeaders(); // Render headers immediately
                renderEmailView("rendered"); // ✅ Automatically display email upon load
            }

            analyzeEmail();

            // ✅ Function to display the cached security data
            function renderSecurity() {
                if (!cachedSecurityData) {
                    document.getElementById("tabContent").innerHTML = `<p class="text-center text-gray-500">Loading security data...</p>`;
                    return;
                }

                function getBadge(status) {
                    switch (status) {
                        case "PASS":
                            return `<span class="px-2 py-1 text-green-700 bg-green-200 rounded-md">✅ PASS</span>`;
                        case "SOFTFAIL":
                            return `<span class="px-2 py-1 text-yellow-700 bg-yellow-200 rounded-md">⚠️ SOFTFAIL</span>`;
                        case "NEUTRAL":
                            return `<span class="px-2 py-1 text-yellow-700 bg-yellow-200 rounded-md">⚠️ NEUTRAL</span>`;
                        case "NONE":
                            return `<span class="px-2 py-1 text-gray-700 bg-gray-200 rounded-md">➖ NONE</span>`;
                        case "TEMPERROR":
                            return `<span class="px-2 py-1 text-orange-700 bg-orange-200 rounded-md">⚠️ TEMPERROR</span>`;
                        case "PERMERROR":
                            return `<span class="px-2 py-1 text-orange-700 bg-orange-200 rounded-md">⚠️ PERMERROR</span>`;
                        case "FAIL":
                            return `<span class="px-2 py-1 text-red-700 bg-red-200 rounded-md">❌ FAIL</span>`;
                        default:
                            return `<span class="px-2 py-1 text-gray-700 bg-gray-200 rounded-md">❓ ${status}</span>`;
                    }
                }

                const spoofText = cachedSecurityData.spoofable
                    ? `<span class="text-yellow-700 font-semibold">⚠️ This domain can potentially be spoofed.</span>`
                    : `<span class="text-green-700 font-semibold">✅ This domain is strongly protected against spoofing.</span>`;

                document.getElementById("tabContent").innerHTML = `
        <!-- SPF Section -->
        <div class="border rounded p-5 mb-4 shadow-md bg-white">
            <h3 class="font-semibold text-gray-800 text-xl mb-2">SPF</h3>
            <p><strong>Result:</strong> ${getBadge(cachedSecurityData.spfStatus)}</p>
            <p><strong>Originating IP:</strong> <span class="text-blue-600">${cachedSecurityData.originatingIp}</span></p>
            <p><strong>Return-Path domain:</strong> <span class="text-gray-700">${cachedSecurityData.returnPathDomain}</span></p>
            <p><strong>SPF Record:</strong> <span class="text-gray-700 break-all">${cachedSecurityData.spfRecord}</span></p>
        </div>

        <!-- DKIM Section -->
        <div class="border rounded p-5 mb-4 shadow-md bg-white">
            <h3 class="font-semibold text-gray-800 text-xl mb-2">DKIM</h3>
            <p><strong>Result:</strong> ${getBadge(cachedSecurityData.dkimStatus)}</p>
            <p><strong>Verification(s):</strong> ${cachedSecurityData.dkimSignatureCount} Signature(s) - ${getBadge(cachedSecurityData.dkimStatus)}</p>
            <p><strong>Selector:</strong> <span class="text-gray-700 break-all">${cachedSecurityData.dkimSelector !== "N/A" ? cachedSecurityData.dkimSelector + "._domainkey." + cachedSecurityData.signingDomain : "No DKIM selector found."}</span></p>
            <p><strong>Signing domain:</strong> <span class="text-blue-600">${cachedSecurityData.signingDomain}</span></p>
            <p><strong>Verification:</strong> ${getBadge(cachedSecurityData.dkimStatus)}</p>
        </div>

        <!-- DMARC Section -->
        <div class="border rounded p-5 mb-4 shadow-md bg-white">
            <h3 class="font-semibold text-gray-800 text-xl mb-2">DMARC</h3>
            <p><strong>Result:</strong> ${getBadge(cachedSecurityData.dmarcStatus)}</p>
            <p><strong>From domain:</strong> <span class="text-blue-600">${cachedSecurityData.returnPathDomain}</span></p>
            <p><strong>DMARC Record:</strong> <span class="text-gray-700 break-all">${cachedSecurityData.dmarcRecord}</span></p>
        </div>

        <!-- Spoof Verdict Section -->
        <div class="border rounded p-5 shadow-md bg-white">
            <h3 class="font-semibold text-gray-800 text-xl mb-2">Verdict</h3>
            <p>${spoofText}</p>
        </div>
    `;
            }

            function renderXHeaders() {
                document.getElementById("tabContent").innerHTML = renderTable(
                    ["X-Header", "Value"],
                    Object.entries(emailData.analysis?.headers?.["x-headers"] || {}).map(([key, value]) => [key, value])
                );
            }

            function renderEmailView(view) {
                const emailHtml = emailData.analysis?.body?.html || "<p>No HTML content available.</p>";
                const emailPlaintext = emailData.analysis?.body?.plaintext || "No plaintext available.";
                const rawEmail = emailData.analysis?.raw_email || "No raw email available";

                // Hide all views first
                document.getElementById("emailRender").classList.add("hidden");
                document.getElementById("emailPlaintext").classList.add("hidden");
                document.getElementById("emailHtml").classList.add("hidden");
                document.getElementById("emailSource").classList.add("hidden");

                if (view === "rendered") {
                    const emailIframe = document.getElementById("emailRender").contentWindow.document;
                    emailIframe.open();
                    emailIframe.write(emailHtml);
                    emailIframe.close();
                    document.getElementById("emailRender").classList.remove("hidden");
                } else if (view === "plaintext") {
                    const ptEl = document.getElementById("emailPlaintext");
                    ptEl.textContent = emailPlaintext;
                    ptEl.classList.remove("hidden");
                    Prism.highlightElement(ptEl);
                } else if (view === "html") {
                    const htmlEl = document.getElementById("emailHtml");
                    htmlEl.textContent = emailHtml;
                    htmlEl.classList.remove("hidden");
                    Prism.highlightElement(htmlEl);
                } else if (view === "source") {
                    const srcEl = document.getElementById("emailSource");
                    srcEl.textContent = rawEmail;
                    srcEl.classList.remove("hidden");
                    Prism.highlightElement(srcEl);
                }
            }

            document.querySelectorAll("[data-tab]").forEach((tab, index) => {
                tab.addEventListener("click", function () {
                    document.querySelectorAll("[data-tab]").forEach(t => t.classList.remove("tab-active"));
                    this.classList.add("tab-active");

                    const tabActions = {
                        headers: renderHeaders,
                        xheaders: renderXHeaders,
                        security: renderSecurity,
                        attachments: renderAttachments,
                        iocs: renderIOCs,
                        traceroute: renderTraceroute // ✅ New Tab Functionality
                    };

                    tabActions[this.dataset.tab] && tabActions[this.dataset.tab]();
                });

                if (index === 0) tab.click();
            });

            // ✅ Call analyzeEmail() when email is submitted
            analyzeEmail();

            document.querySelectorAll("[data-view]").forEach(view => {
                view.addEventListener("click", function () {
                    document.querySelectorAll("[data-view]").forEach(v => v.classList.remove("tab-active"));
                    this.classList.add("tab-active");
                    renderEmailView(this.dataset.view);
                });
            });

            renderHeaders();

            document.addEventListener('click', function (e) {
                const target = e.target;
                // Check if the clicked element or its parent has the .ripple-button class
                const rippleButton = target.closest('.ripple-button');
                if (!rippleButton) return;

                // Create the ripple circle
                const circle = document.createElement('span');
                const diameter = Math.max(rippleButton.clientWidth, rippleButton.clientHeight);
                const radius = diameter / 2;

                circle.style.width = circle.style.height = `${diameter}px`;
                circle.style.left = `${e.clientX - (rippleButton.getBoundingClientRect().left + radius)}px`;
                circle.style.top = `${e.clientY - (rippleButton.getBoundingClientRect().top + radius)}px`;
                circle.classList.add('ripple');

                // Remove any existing ripple
                const existingRipple = rippleButton.getElementsByClassName('ripple')[0];
                if (existingRipple) {
                    existingRipple.remove();
                }

                // Append the new ripple
                rippleButton.appendChild(circle);
            });
        });
    </script>
</body>

</html>