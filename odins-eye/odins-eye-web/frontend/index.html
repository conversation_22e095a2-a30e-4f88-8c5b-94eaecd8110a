<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON>'s Eye</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="icon" type="image/png" href="assets/logo.png">
    <style>
        /* Brand color usage: Indigo from Tailwind or your custom variable */
        :root {
            --brand-color: #6366F1;
            /* Indigo-500, adjust as needed */
        }

        /* Hover highlight for the drop area */
        .drop-area-hover {
            background-color: rgba(99, 102, 241, 0.05);
            border-color: var(--brand-color) !important;
        }

        #customAlert {
            background-color: #fff;
            color: #1f2937;
            /* gray-800 */
            box-shadow: var(--shadow-md);
            border-left-width: 6px;
            border-radius: 8px;
            transition: var(--transition);
            font-size: 15px;
        }

        #customAlert.success {
            border-color: #10b981;
            /* emerald-500 */
        }

        #customAlert.info {
            border-color: var(--primary);
        }

        #customAlert.error {
            border-color: #ef4444;
            /* red-500 */
        }

        #customAlert.warning {
            border-color: #f59e0b;
            /* amber-500 */
        }

        @keyframes fadeOut {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0.6;
            }
        }

        .animate-fade {
            animation: fadeOut 0.5s ease-out forwards;
        }
    </style>
    <script>
        window.addEventListener("DOMContentLoaded", () => {
            // Attempt to get the port from preload script or fallback
            window.backendPort = window.electronAPI?.backendPort || 8080;
        });
    </script>
</head>

<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <!-- 👇 Vertical stack: Title + Card -->
    <div class="flex flex-col items-center w-full max-w-md space-y-6">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Odin’s Eye</h1>
            <h4 class="text-xl font-medium text-gray-700 mt-1">Doppel Email Analysis</h4>
        </div>

        <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center mx-2">
            <!-- Logo & Title -->
            <div class="mb-4">
                <img src="/assets/logo.png" alt="Odin's Eye Logo" class="h-16 mx-auto">
            </div>
            <h1 class="text-xl font-semibold mb-1">Upload an Email</h1>
            <p class="text-gray-500 mb-4">Drag & drop an email file (<em>.eml, .msg</em>) or click to select one.</p>

            <!-- Drag & Drop Area -->
            <div id="dropArea"
                class="border-2 border-dashed border-indigo-400 p-6 rounded-lg text-gray-500 cursor-pointer transition-colors">
                <!-- A small icon to visually represent "upload" -->
                <div class="text-indigo-400 mb-2">
                    <svg class="mx-auto w-8 h-8" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3 15a4 4 0 00 4 4h10a4 4 0 004-4V7a4 4 0 00 -4-4H7a4 4 0 00 -4 4v8z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8 10l4-4 4 4M12 6v10" />
                    </svg>
                </div>
                <label for="fileInput" id="fileLabel" class="cursor-pointer block">
                    Click or drag a file here
                </label>
                <input type="file" id="fileInput" accept=".eml,.msg" class="hidden">
            </div>

            <!-- Upload Button (Initially Disabled) -->
            <button id="uploadButton"
                class="mt-4 bg-gray-400 text-white px-4 py-2 rounded-md w-full cursor-not-allowed transition-colors"
                disabled>
                Upload File
            </button>

            <!-- Loading Animation -->
            <div id="loading" class="hidden mt-4">
                <p class="text-gray-600">Processing...</p>
                <div class="animate-spin rounded-full h-10 w-10 border-t-4 border-indigo-500 mx-auto mt-2"></div>
            </div>

            <!-- Optional Clear File Link -->
            <div id="clearFileLink" class="hidden mt-2">
                <button class="text-sm text-red-500 underline">Clear selected file</button>
            </div>
        </div>
    </div>

    <div id="customAlert"
        class="hidden fixed top-6 left-1/2 transform -translate-x-1/2 bg-white text-gray-800 border-l-4 border-red-500 px-6 py-4 rounded-lg shadow-md z-50 max-w-md w-full animate-fadeInSlideDown">
        <strong class="font-semibold block mb-1">Error</strong>
        <span id="customAlertMessage">Something went wrong.</span>
    </div>

    <script>
        function showAlert(message, type = "info", duration = 4000) {
            const alertEl = document.getElementById("customAlert");
            const messageEl = document.getElementById("customAlertMessage");

            alertEl.className = `fixed top-6 left-1/2 transform -translate-x-1/2 px-6 py-4 rounded-lg shadow-md z-50 max-w-md w-full animate-fadeInSlideDown ${type}`;
            alertEl.classList.remove("hidden");
            alertEl.classList.add(type);
            messageEl.textContent = message;

            setTimeout(() => {
                alertEl.classList.add("hidden");
                alertEl.classList.remove(type);
            }, duration);
        }

        const fileInput = document.getElementById("fileInput");
        const fileLabel = document.getElementById("fileLabel");
        const uploadButton = document.getElementById("uploadButton");
        const dropArea = document.getElementById("dropArea");
        const loadingIndicator = document.getElementById("loading");
        const clearFileLink = document.getElementById("clearFileLink");

        // ✅ Enable the upload button once a file is selected
        function enableUploadUI(filename) {
            fileLabel.textContent = `📩 ${filename}`;
            fileLabel.classList.add("text-indigo-600", "font-semibold");
            uploadButton.classList.remove("bg-gray-400", "cursor-not-allowed");
            uploadButton.classList.add("bg-indigo-600", "hover:bg-indigo-700");
            uploadButton.disabled = false;
            clearFileLink.classList.remove("hidden");
        }

        // ✅ Reset UI if user clears the file
        function resetUI() {
            fileLabel.textContent = "Click or drag a file here";
            fileLabel.classList.remove("text-indigo-600", "font-semibold");
            uploadButton.classList.add("bg-gray-400", "cursor-not-allowed");
            uploadButton.classList.remove("bg-indigo-600", "hover:bg-indigo-700");
            uploadButton.disabled = true;
            fileInput.value = "";
            clearFileLink.classList.add("hidden");
        }

        // Handle file selection
        fileInput.addEventListener("change", function () {
            if (fileInput.files.length > 0) {
                enableUploadUI(fileInput.files[0].name);
            } else {
                resetUI();
            }
        });

        // ✅ Drag & Drop
        dropArea.addEventListener("dragover", (e) => {
            e.preventDefault();
            dropArea.classList.add("drop-area-hover");
        });
        dropArea.addEventListener("dragleave", () => {
            dropArea.classList.remove("drop-area-hover");
        });
        dropArea.addEventListener("drop", (e) => {
            e.preventDefault();
            dropArea.classList.remove("drop-area-hover");

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                // Check file type here if you want to limit .eml/.msg/
                fileInput.files = files;
                enableUploadUI(files[0].name);
            }
        });

        // UI element to show status (you can style this or animate as needed)
        const uploadButtonText = document.getElementById("uploadButton");

        // 🔁 Docker Polling Feedback (show pulse + spinner)
        window.electronAPI?.onDockerPolling((_event, msg) => {
            uploadButtonText.innerHTML = `
        <i class="fas fa-spinner fa-spin mr-2"></i>
        Waiting for Docker… ${msg}
    `;
            uploadButtonText.disabled = true;
            uploadButtonText.classList.remove("bg-indigo-600", "hover:bg-indigo-700");
            uploadButtonText.classList.add("bg-yellow-400", "animate-pulse");
        });

        // ✅ Docker Ready Feedback (reset + fade)
        window.electronAPI?.onDockerStarted(() => {
            uploadButtonText.textContent = "Docker is ready!";
            uploadButtonText.disabled = fileInput.files.length === 0;
            uploadButtonText.classList.remove("bg-yellow-400", "animate-pulse");
            uploadButtonText.classList.add("bg-indigo-600", "hover:bg-indigo-700", "animate-fade");

            setTimeout(() => {
                uploadButtonText.classList.remove("animate-fade");
                uploadButtonText.textContent = "Upload File";
            }, 1000);
        });

        // ✅ Clear File
        clearFileLink.addEventListener("click", resetUI);

        // ✅ Upload Logic
        uploadButton.addEventListener("click", async function () {
            let file = fileInput.files[0];
            if (!file) {
                showAlert("Please select a file first.", "warning");
                return;
            }

            loadingIndicator.classList.remove("hidden");

            let formData = new FormData();
            formData.append("file", file);

            try {
                console.log("📨 Uploading email for analysis...");
                let response = await fetch("http://localhost/api/email/analyze", {
                    method: "POST",
                    body: formData,
                });

                let data = await response.json();
                loadingIndicator.classList.add("hidden");

                if (response.ok) {
                    console.log("✅ Upload successful:", data);
                    showAlert("Email uploaded successfully!", "success");
                    let subject = data?.analysis?.headers?.subject || "No Subject";
                    localStorage.setItem("emailData", JSON.stringify(data));
                    window.location.href = "analysis.html?subject=" + encodeURIComponent(subject);
                } else {
                    console.error("🚨 Upload failed:", data);
                    showAlert("Upload failed: " + (data.detail || "Unknown error"), "error");
                }
            } catch (error) {
                loadingIndicator.classList.add("hidden");
                console.error("🚨 Error uploading file:", error);
                showAlert("Error uploading file: " + error.message, "error");
            }
        });
    </script>
</body>

</html>